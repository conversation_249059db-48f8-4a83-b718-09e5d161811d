<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PDF to Image Viewer</title>
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 10px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 100%;
      padding: 10px;
    }
    .pdf-container {
      position: relative;
      width: 100%;
      margin-bottom: 20px;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .pdf-viewer-container {
      position: relative;
      width: 100%;
      height: 100vh;
      max-height: 800px;
      overflow: hidden;
    }
    .pdf-viewer {
      width: 100%;
      height: 100%;
      border: none;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .pdf-fallback {
      padding: 20px;
      text-align: center;
      background: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    .pdf-fallback a {
      display: inline-block;
      padding: 10px 20px;
      background: #007bff;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin: 10px 0;
    }
    .pdf-image {
      width: 100%;
      height: auto;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
      background: white;
      border-radius: 8px;
      transition: transform 0.3s ease;
    }
    .pdf-image:hover {
      transform: scale(1.02);
    }
    .navigation {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin: 20px 0;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 0 8px rgba(0,0,0,0.1);
    }
    .nav-button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
    }
    .nav-button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    .page-number {
      padding: 8px 16px;
      background: white;
      border-radius: 5px;
      box-shadow: 0 0 4px rgba(0,0,0,0.1);
    }
    @media (max-width: 768px) {
      .image-grid {
        grid-template-columns: 1fr;
        padding: 10px;
      }
      body {
        padding: 5px;
      }
      .pdf-viewer-container {
        height: 80vh;
      }
      .navigation {
        flex-wrap: wrap;
      }
      .pdf-container {
        margin-bottom: 10px;
      }
      h1, h2 {
        font-size: 20px;
        margin: 10px 0;
      }
    }
  </style>
</head>
<body>

  <div class="container">
    <h1>PDF Document</h1>
    
    <div class="pdf-container">
      <h2>Original PDF</h2>
      <div class="pdf-viewer-container">
        <iframe 
          class="pdf-viewer" 
          src="images/AnnachiCrackersTariff.pdf#toolbar=0&navpanes=0&scrollbar=1&view=FitH" 
          type="application/pdf"
          frameborder="0"
          scrolling="auto">
          <div class="pdf-fallback">
            <p>Your device doesn't support PDF viewing directly.</p>
            <a href="images/AnnachiCrackersTariff.pdf" class="nav-button" download>
              Download PDF
            </a>
            <a href="#image-preview" class="nav-button" style="background: #28a745;">
              View as Images
            </a>
          </div>
        </iframe>
      </div>
    </div>

    <!-- <h2>Image Preview</h2>
    <div class="navigation">
      <button class="nav-button" onclick="previousPage()" id="prevBtn">← Previous</button>
      <span class="page-number">Page <span id="currentPage">1</span> of <span id="totalPages">4</span></span>
      <button class="nav-button" onclick="nextPage()" id="nextBtn">Next →</button>
    </div>
    
    <div class="image-grid" id="imageGrid">
      <img src="images/AnnachiCrackersTariff_page-0001.jpg" alt="PDF Page 1" class="pdf-image">
      <img src="images/AnnachiCrackersTariff_page-0002.jpg" alt="PDF Page 2" class="pdf-image">
      <img src="images/AnnachiCrackersTariff_page-0003.jpg" alt="PDF Page 3" class="pdf-image">
      <img src="images/AnnachiCrackersTariff_page-0004.jpg" alt="PDF Page 4" class="pdf-image" style="display: none;">
    </div>

    <script>
      const images = document.querySelectorAll('.pdf-image');
      const totalPages = images.length;
      const imagesPerPage = 3;
      let currentPage = 1;

      function updateVisibility() {
        const startIdx = (currentPage - 1) * imagesPerPage;
        const endIdx = startIdx + imagesPerPage;
        
        images.forEach((img, index) => {
          if (index >= startIdx && index < endIdx) {
            img.style.display = 'block';
          } else {
            img.style.display = 'none';
          }
        });

        document.getElementById('currentPage').textContent = currentPage;
        document.getElementById('totalPages').textContent = Math.ceil(totalPages / imagesPerPage);
        
        document.getElementById('prevBtn').disabled = currentPage === 1;
        document.getElementById('nextBtn').disabled = currentPage >= Math.ceil(totalPages / imagesPerPage);
      }

      function nextPage() {
        if (currentPage < Math.ceil(totalPages / imagesPerPage)) {
          currentPage++;
          updateVisibility();
        }
      }

      function previousPage() {
        if (currentPage > 1) {
          currentPage--;
          updateVisibility();
        }
      }

      // Initialize
      updateVisibility();
    </script> -->
</body>
</html>
